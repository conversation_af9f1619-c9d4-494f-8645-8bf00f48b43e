#!/usr/bin/env python3
"""
Genesis Telegram Referral Bot
Simplified version focused on core referral functionality
"""

import logging
import asyncio
import signal
import sys
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton, CopyTextButton
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes

from config import Config
from src.database import Database
from src.models.user import User
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.models.referral import Referral, ReferralStatus
from src.services.user_service import UserService
from src.services.referral_service import ReferralService
from src.services.transaction_service import TransactionService
from src.utils.logger import setup_logger, setup_production_logging
from src.utils.security import rate_limiter, ban_manager

# Setup logging
logger = setup_logger(__name__)
startup_logger = setup_production_logging()

class GenesisBotApp:
    """Genesis Telegram Referral Bot Application"""
    
    def __init__(self):
        self.application = None
        self.database = None
        self.services = {}

        # Shutdown management
        self.shutdown_event = asyncio.Event()
        self.shutdown_in_progress = False
        
    async def initialize_async_components(self):
        """Initialize async components"""
        try:
            startup_logger.info("🤖 Starting Genesis Telegram Referral Bot...")
            startup_logger.info("🔄 Mode: Long Polling (no webhook/domain required)")
            
            # Validate configuration
            Config.validate_config()
            logger.info("✅ Configuration validated")
            
            # Initialize database connection
            startup_logger.info("🔄 Initializing database connection...")
            self.database = Database()
            db_connected = await self.database.connect()

            if db_connected:
                startup_logger.info("✅ Database connected successfully")
            else:
                startup_logger.error("❌ Database connection failed")
                raise Exception("Database connection failed")
            
            # Initialize simplified services
            startup_logger.info("🔄 Initializing simplified services...")
            self.services = {
                'user': UserService(self.database),
                'referral': ReferralService(self.database),
                'transaction': TransactionService(self.database)
            }

            # Create direct service attribute references for admin panel
            self.user_service = self.services['user']
            self.referral_service = self.services['referral']
            self.transaction_service = self.services['transaction']

            startup_logger.info("✅ Services initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize async components: {e}")
            raise

    def get_main_keyboard(self):
        """Get main menu keyboard with 5-button layout"""
        keyboard = [
            [KeyboardButton("🚀 Earn Genesis Token"), KeyboardButton("💰 Genesis Token Balance")],
            [KeyboardButton("How to Earn Genesis Token❓")],
            [KeyboardButton("🏆 Top 5 Users"), KeyboardButton("ℹ️ About Genesis Bot")]
        ]
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False,
            input_field_placeholder="Choose your option! 🎁✨"
        )
    
    async def check_user_permissions(self, user_id: int) -> dict:
        """Check user permissions and restrictions"""
        result = {
            'allowed': True,
            'reason': '',
            'banned': False,
            'rate_limited': False
        }
        
        # Check if user is banned
        try:
            user = await self.services['user'].get_user(user_id)
            if user and user.is_banned:
                result['allowed'] = False
                result['banned'] = True
                result['reason'] = user.ban_reason or "You are banned from using this bot."
                return result
        except:
            pass  # Continue if user service fails
        
        # Check temporary ban
        if ban_manager.is_temp_banned(user_id):
            remaining = ban_manager.get_ban_time_remaining(user_id)
            result['allowed'] = False
            result['banned'] = True
            result['reason'] = f"You are temporarily banned. Time remaining: {remaining}"
            return result
        
        # Check rate limiting
        if rate_limiter.is_rate_limited(user_id):
            result['allowed'] = False
            result['rate_limited'] = True
            result['reason'] = "You are sending messages too quickly. Please slow down."
            return result
        
        return result
    
    def extract_referral_code(self, text: str):
        """Extract referral code from start command"""
        if not text or not text.startswith('/start'):
            return None
        
        parts = text.split()
        if len(parts) > 1:
            code = parts[1].strip()
            if len(code) == 8 and code.isalnum():
                return code.upper()
        
        return None
    
    # ==================== COMMAND HANDLERS ====================

    async def _create_user_data_structure(self, user) -> Dict[str, Any]:
        """Create user data structure from Telegram user object"""
        return {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'language_code': user.language_code,
            'is_bot': user.is_bot,
            'is_premium': getattr(user, 'is_premium', False)
        }

    async def _store_user_data(self, user_data: Dict[str, Any], referral_code: Optional[str]) -> Optional[User]:
        """Store user data in database with referral processing"""
        try:
            logger.debug(f"Storing user data for user {user_data['user_id']}")
            if referral_code:
                logger.debug(f"Referral code provided: {referral_code}")

            db_user = await self.services['user'].create_user(user_data, referral_code)

            if db_user:
                logger.debug(f"User data stored for user {user_data['user_id']}")
            else:
                logger.error(f"❌ FAILED: create_user returned None for user {user_data['user_id']}")

            return db_user
        except Exception as e:
            logger.error(f"❌ EXCEPTION: Failed to store user data for user {user_data['user_id']}: {e}")
            return None

    async def _handle_member_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                  user, db_user: Optional[User], referral_code: Optional[str]):
        """Handle user who is already a member of required channels"""
        # User is member of the required channel
        await self._update_user_channel_status(user.id, True)

        # Process referral after channel verification - validate immediately since user is already a member
        if referral_code and db_user and db_user.referred_by:
            await self._process_referral(db_user.user_id, db_user.referred_by, referral_code, validate_immediately=True)

        # Also validate any existing pending referrals for this user
        await self._validate_pending_referrals(user.id)

        # Show welcome
        await self._show_welcome_response(update, context, user, db_user, referral_code)

    async def _handle_non_member_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                      user, db_user: Optional[User], referral_code: Optional[str]):
        """Handle user who is not a member of required channels"""
        # User is not member of required channels
        await self._update_user_channel_status(user.id, False)

        # Create pending referral if referral code exists
        if referral_code and db_user and db_user.referred_by:
            await self._process_referral(db_user.user_id, db_user.referred_by, referral_code, validate_immediately=False)

        # Store referral code for processing after channel join (for legacy support)
        if referral_code:
            context.user_data['pending_referral'] = referral_code
            context.user_data['pending_referrer'] = db_user.referred_by if db_user else None

        # Show channel join interface
        await self._show_channel_join_interface(update, context, user)

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with enhanced referral flow - refactored for clarity"""
        try:
            user = update.effective_user

            # Check user permissions
            permissions = await self.check_user_permissions(user.id)
            if not permissions['allowed']:
                try:
                    await update.message.reply_text(permissions['reason'])
                except Exception:
                    pass
                return

            # Extract referral code from command arguments
            referral_code = None
            if context.args:
                referral_code = self.extract_referral_code(f"/start {context.args[0]}")

            # Create and store user data
            user_data = await self._create_user_data_structure(user)
            db_user = await self._store_user_data(user_data, referral_code)

            # Perform channel membership verification
            logger.debug(f"Verifying channel membership for user {user.id}")
            is_member = await self._verify_required_channels(user.id, context)

            # Handle user based on membership status
            if is_member:
                await self._handle_member_user(update, context, user, db_user, referral_code)
            else:
                await self._handle_non_member_user(update, context, user, db_user, referral_code)

        except Exception as e:
            # Handle bot blocked scenarios silently
            error_msg = str(e).lower()
            if "forbidden" in error_msg and "bot was blocked" in error_msg:
                logger.debug(f"User {user.id} has blocked the bot")
                return

            logger.error(f"Error in start command: {e}")
            try:
                await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **An error occurred. Please try again.** ⚠️
                """)
            except Exception:
                pass

    async def _verify_required_channels(self, user_id: int, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Verify if user is member of the required channel"""
        try:
            channel_id = Config.get_required_channel_id()
            logger.debug(f"Starting channel verification for user {user_id}")

            try:
                logger.info(f"Checking membership in channel: {channel_id}")
                member = await context.bot.get_chat_member(chat_id=channel_id, user_id=user_id)
                logger.info(f"User {user_id} status in channel {channel_id}: {member.status}")

                valid_statuses = ['member', 'administrator', 'creator']
                invalid_statuses = ['left', 'kicked', 'restricted']

                if member.status in invalid_statuses:
                    logger.info(f"❌ User {user_id} rejected - invalid status in channel {channel_id}: {member.status}")
                    return False

                if member.status in valid_statuses:
                    logger.info(f"✅ User {user_id} accepted - valid status in channel {channel_id}: {member.status}")
                    return True
                else:
                    logger.warning(f"⚠️ User {user_id} has unknown status in channel {channel_id}: {member.status}")
                    return False

            except Exception as e:
                error_msg = str(e).lower()
                if "chat not found" in error_msg:
                    logger.error(f"Channel {channel_id} not found - bot may not be admin or channel ID incorrect")
                elif "user not found" in error_msg:
                    logger.error(f"User {user_id} not found in channel {channel_id}")
                elif "forbidden" in error_msg:
                    logger.error(f"Bot lacks permission to check membership in channel {channel_id}")
                else:
                    logger.error(f"Unexpected error checking membership for user {user_id} in channel {channel_id}: {e}")
                return False

        except Exception as e:
            logger.error(f"Critical error in channel membership verification: {e}")
            return False

    async def _update_user_channel_status(self, user_id: int, has_joined: bool):
        """Update user's channel join status in database"""
        try:
            user = await self.services['user'].get_user(user_id)
            if user:
                user.has_joined_channels = has_joined
                await self.services['user'].update_user(user)
                logger.debug(f"Updated channel status for user {user_id}: has_joined_channels = {has_joined}")
        except Exception as e:
            logger.error(f"Failed to update channel status for user {user_id}: {e}")

    async def _safe_edit_message(self, query, text: str, parse_mode: str = None, reply_markup=None):
        """Safely edit message - handles both photo and text messages"""
        try:
            # Check if the original message has a photo (caption) or is text-only
            if query.message.photo:
                # Original message is a photo with caption - use edit_message_caption
                await query.edit_message_caption(
                    caption=text,
                    parse_mode=parse_mode,
                    reply_markup=reply_markup
                )
                logger.debug("Successfully edited photo message caption")
            else:
                # Original message is text-only - use edit_message_text
                await query.edit_message_text(
                    text=text,
                    parse_mode=parse_mode,
                    reply_markup=reply_markup
                )
                logger.debug("Successfully edited text message")

        except Exception as e:
            logger.error(f"Failed to edit message safely: {e}")
            # Fallback: try to send a new message if editing fails
            try:
                await query.message.reply_text(
                    text=text,
                    parse_mode=parse_mode,
                    reply_markup=reply_markup
                )
                logger.debug("Sent new message as fallback")
            except Exception as fallback_error:
                logger.error(f"Fallback message sending also failed: {fallback_error}")
                # Last resort: just answer the callback query
                try:
                    await query.answer("✅ Action completed")
                except:
                    pass

    async def _process_referral(self, user_id: int, referrer_id: int, referral_code: str, validate_immediately: bool = False):
        """Process referral - create pending or validated referral based on channel membership"""
        try:
            if validate_immediately:
                # User is already a channel member - create and immediately validate referral
                referral = await self.services['referral'].create_referral(referrer_id, user_id, referral_code, validate_immediately=True)
                if referral:
                    logger.info(f"✅ Referral processed and validated immediately: {referrer_id} -> {user_id}")
                else:
                    logger.error(f"❌ Failed to create validated referral: {referrer_id} -> {user_id}")
            else:
                # User is not a channel member yet - create pending referral
                referral = await self.services['referral'].create_pending_referral(referrer_id, user_id, referral_code)
                if referral:
                    logger.info(f"⏳ Pending referral created: {referrer_id} -> {user_id} (awaiting channel verification)")
                else:
                    logger.error(f"❌ Failed to create pending referral: {referrer_id} -> {user_id}")
        except Exception as e:
            logger.error(f"Error processing referral: {e}")

    async def _validate_pending_referrals(self, user_id: int):
        """Validate all pending referrals for a user who just joined the channel"""
        try:
            validated_referrals = await self.services['referral'].validate_pending_referrals(user_id)
            if validated_referrals:
                logger.info(f"✅ Validated {len(validated_referrals)} pending referrals for user {user_id}")
                return len(validated_referrals)
            return 0
        except Exception as e:
            logger.error(f"Error validating pending referrals for user {user_id}: {e}")
            return 0

    def _generate_welcome_message(self, user, db_user, referral_link: str) -> str:
        """Generate welcome message text"""
        return f"""
╔══════════════════════════════╗
║    🎉 **WELCOME TO GENESIS BOT** 🎉    ║
╚══════════════════════════════╝

👋 **Hello {user.first_name}!**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💎 **YOUR GENESIS TOKEN BALANCE**
🪙 **{int(db_user.balance) if db_user else 0}** *Genesis Tokens*

🔗 **YOUR REFERRAL LINK**
📋 `{referral_link}`

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🚀 **HOW TO EARN GENESIS TOKENS:**
💰 *Share your referral link with friends*
🎁 *Earn* **{Config.REFERRAL_REWARD} Genesis Tokens** *per referral*
🎉 *Your friends get* **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens** *for joining!*

🏆 *The* **Top 5 users** *on the leaderboard each week win* **guaranteed prizes**
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Start earning now by inviting your friends!** 🚀
        """

    async def _send_welcome_message(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int,
                                    welcome_message: str, inline_reply_markup):
        """Send welcome message with photo or text fallback"""
        try:
            with open('start.jpg', 'rb') as photo_file:
                await context.bot.send_photo(
                    chat_id=chat_id,
                    photo=photo_file,
                    caption=welcome_message,
                    parse_mode='Markdown',
                    reply_markup=inline_reply_markup
                )
        except FileNotFoundError:
            await context.bot.send_message(
                chat_id=chat_id,
                text=welcome_message,
                parse_mode='Markdown',
                reply_markup=inline_reply_markup
            )

    async def _show_welcome_response(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user, db_user, referral_code):
        """Show welcome response - refactored for clarity"""
        try:
            # Generate referral link
            referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={db_user.referral_code if db_user else 'LOADING'}"

            # Generate welcome message
            welcome_message = self._generate_welcome_message(user, db_user, referral_link)

            # Create inline keyboard with copy referral link button
            copy_text_button = CopyTextButton(text=referral_link)
            inline_keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", copy_text=copy_text_button)]
            ]
            inline_reply_markup = InlineKeyboardMarkup(inline_keyboard)

            # Send welcome message with photo or text fallback
            await self._send_welcome_message(context, update.effective_chat.id, welcome_message, inline_reply_markup)

            # Send the main keyboard as a separate message
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text="🏠 **Choose an option from the menu below:**",
                parse_mode='Markdown',
                reply_markup=self.get_main_keyboard()
            )

            # Mark registration as completed
            if db_user:
                await self.services['user'].mark_registration_completed(db_user.user_id)

        except Exception as e:
            logger.error(f"Error in welcome response: {e}")
            fallback_message = """
╔══════════════════════════════╗
║    🎁 **WELCOME TO GENESIS BOT** 🎉    ║
╚══════════════════════════════╝

🚀 **Start earning Genesis Tokens by inviting friends!** ✨
            """
            try:
                await update.message.reply_text(
                    fallback_message,
                    reply_markup=self.get_main_keyboard()
                )
            except:
                pass

    async def _show_channel_join_interface(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user):
        """Show channel join interface"""
        try:
            join_caption = """
╔══════════════════════════════╗
║  🔐 **CHANNEL VERIFICATION REQUIRED** 🔐  ║
╚══════════════════════════════╝

📢 *Please join our channel to use the bot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Join now to start earning Genesis Tokens!** ✨
            """
            invite_link = Config.get_channel_invite_link()

            keyboard = [
                [InlineKeyboardButton("📢 JOIN CHANNEL", url=invite_link)],
                [InlineKeyboardButton("✅ I HAVE JOINED", callback_data="verify_channels")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                with open('join.jpg', 'rb') as photo_file:
                    await context.bot.send_photo(
                        chat_id=update.effective_chat.id,
                        photo=photo_file,
                        caption=join_caption,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
            except FileNotFoundError:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=join_caption,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing channel join interface: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **An error occurred. Please try again.** ⚠️
            """)

    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle balance command"""
        try:
            user_id = update.effective_user.id
            telegram_user = update.effective_user

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user: {e}")
                user = None

            if not user:
                profile_text = """
👤 **Profile Not Found**

❌ *Please start the bot with /start to create your profile.*
                """
            else:
                # Format join date
                try:
                    if user.created_at and isinstance(user.created_at, datetime):
                        join_date = user.created_at.strftime("%d %b %Y")
                    else:
                        join_date = "Unknown"
                except (AttributeError, ValueError):
                    join_date = "Unknown"

                profile_text = f"""
╔══════════════════════════════╗
║  💎 **{user.get_display_name()}'s GENESIS PROFILE** 💎  ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🪙 **GENESIS TOKEN BALANCE**
💰 **{int(user.balance):,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

👥 **REFERRAL STATISTICS**
🎯 **Total Referrals:** *{user.successful_referrals} people*
💎 **Tokens Earned:** **{int(user.successful_referrals * Config.REFERRAL_REWARD):,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📅 **Member since** *{join_date}* ✨
                """

            await update.message.reply_text(
                profile_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load profile information.** ⚠️
            """)

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            user_id = update.effective_user.id

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            help_text = f"""
╔══════════════════════════════╗
║      🎁 **GENESIS BOT GUIDE** 🎁      ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💰 **REFERRAL REWARDS**
🎯 **Per Referral:** **{Config.REFERRAL_REWARD}** *Genesis Tokens*
🎁 **Friend Bonus:** **{Config.FRIEND_WELCOME_BONUS}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🚀 **HOW TO EARN GENESIS TOKENS**
📤 *Share your referral link with friends*
👥 *Invite friends - earn* **{Config.REFERRAL_REWARD} tokens** *per join!*
📊 *Track progress with your balance*
🏆 *Compete on the leaderboard for* **prizes**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🎯 **QUICK START GUIDE**
**1.** *Get your referral link from* **"Earn Genesis Token"**
**2.** *Share with friends on social media, WhatsApp, X*
**3.** *Earn* **{Config.REFERRAL_REWARD} tokens** *when friends join*
**4.** *Check leaderboard to see your ranking*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📱 **AVAILABLE FEATURES**
🪙 **Genesis Token Balance** - *Check balance & stats*
🚀 **Earn Genesis Token** - *Get referral link & stats*
🏆 **Top 5 Users** - *View leaderboard & ranking*
ℹ️ **About Genesis Bot** - *Learn about rewards*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🔗 **REFERRAL SYSTEM**
*When someone joins using your link:*
✅ *You earn* **{Config.REFERRAL_REWARD} Genesis Tokens** *instantly*
✅ *Your friend gets* **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens**
✅ *Both accounts credited automatically*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🏆 **LEADERBOARD & REWARDS**
🥇 **Top 5 users** *each week win* **guaranteed prizes**
🎉 **Weekly giveaways** *for all active users*
📈 *Climb by earning more* **Genesis Tokens**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 **PRO TIPS**
📢 *Share your link in multiple places*
👥 *Engage with friends to encourage joining*
📊 *Check balance and ranking regularly*
🎯 *Participate in* **weekly competitions**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

❓ **Need Help?**
*Genesis Bot - Your gateway to airdrops, giveaways, and* **exclusive rewards!** 🌟
            """

            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=self.get_main_keyboard()
            )

        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **An error occurred. Please try again.** ⚠️
            """)

    async def admin_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /admin command - Admin panel access"""
        try:
            user_id = update.effective_user.id

            # Check if user is admin
            if user_id not in Config.ADMIN_USER_IDS:
                await update.message.reply_text("""
╔══════════════════════════════╗
║        🚫 **ACCESS DENIED** 🚫        ║
╚══════════════════════════════╝

❌ **You don't have admin privileges.**
                """)
                return

            # Initialize admin session
            context.user_data['admin_mode'] = True
            context.user_data['admin_page'] = 'main'

            await self._show_admin_main_menu(update, context)

        except Exception as e:
            logger.error(f"Error in admin command: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to access admin panel.** ⚠️
            """)

    # ==================== ADMIN PANEL METHODS ====================

    async def _show_admin_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show admin main menu"""
        try:
            # Get bot statistics
            stats = await self._get_bot_statistics()

            admin_text = f"""
╔══════════════════════════════╗
║    🔧 **GENESIS BOT ADMIN PANEL** 🔧    ║
╚══════════════════════════════╝

📊 **BOT STATISTICS OVERVIEW**

👥 **Total Users:** `{stats['total_users']}`
🏆 **Top Referrer:** `{stats['top_referrer']['display']}` ({stats['top_referrer']['count']} referrals)
🔗 **Total Referrals:** `{stats['total_referrals']}`

📅 **USER GROWTH**
• **Today:** `{stats['users_today']}` new users
• **Yesterday:** `{stats['users_yesterday']}` new users
• **Last 7 days:** `{stats['users_week']}` new users

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🛠️ **Select an admin function below:**
            """

            keyboard = [
                [KeyboardButton("📊 Bot Statistics"), KeyboardButton("👥 User Management")],
                [KeyboardButton("🏆 Prize Management"), KeyboardButton("📋 Full Leaderboard")],
                [KeyboardButton("🔙 Exit Admin Panel")]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                admin_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing admin main menu: {e}")
            await update.message.reply_text("❌ **Error loading admin panel.**")

    async def _get_bot_statistics(self):
        """Get comprehensive bot statistics"""
        try:
            from datetime import datetime, timezone, timedelta

            # Get current time
            now = datetime.now(timezone.utc)
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            yesterday_start = today_start - timedelta(days=1)
            week_start = today_start - timedelta(days=7)

            # Total users
            total_users = await self.database.users.count_documents({})

            # Users joined today
            users_today = await self.database.users.count_documents({
                'created_at': {'$gte': today_start}
            })

            # Users joined yesterday
            users_yesterday = await self.database.users.count_documents({
                'created_at': {
                    '$gte': yesterday_start,
                    '$lt': today_start
                }
            })

            # Users joined in last 7 days
            users_week = await self.database.users.count_documents({
                'created_at': {'$gte': week_start}
            })

            # Total referrals
            total_referrals = await self.database.referrals.count_documents({
                'status': 'completed'
            })

            # Top referrer
            top_referrer_data = await self.database.users.find_one(
                {'referral_count': {'$gt': 0}},
                sort=[('referral_count', -1)]
            )

            if top_referrer_data:
                top_referrer = {
                    'display': f"@{top_referrer_data.get('username', 'N/A')} (ID: {top_referrer_data['user_id']})",
                    'count': top_referrer_data.get('referral_count', 0)
                }
            else:
                top_referrer = {
                    'display': "No referrals yet",
                    'count': 0
                }

            return {
                'total_users': total_users,
                'users_today': users_today,
                'users_yesterday': users_yesterday,
                'users_week': users_week,
                'total_referrals': total_referrals,
                'top_referrer': top_referrer
            }

        except Exception as e:
            logger.error(f"Error getting bot statistics: {e}")
            return {
                'total_users': 0,
                'users_today': 0,
                'users_yesterday': 0,
                'users_week': 0,
                'total_referrals': 0,
                'top_referrer': {'display': 'Error loading', 'count': 0}
            }

    async def _handle_admin_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str):
        """Handle admin panel messages"""
        try:
            if message_text == "🔙 Exit Admin Panel":
                context.user_data['admin_mode'] = False
                context.user_data['admin_page'] = None
                await update.message.reply_text(
                    """
╔══════════════════════════════╗
║    🚪 **EXITED ADMIN PANEL** 🚪    ║
╚══════════════════════════════╝

✨ **Returned to user mode** ✨
                    """,
                    reply_markup=self.get_main_keyboard(),
                    parse_mode='Markdown'
                )
                return

            elif message_text == "📊 Bot Statistics":
                await self._show_detailed_statistics(update, context)
            elif message_text == "👥 User Management":
                await self._show_user_management_menu(update, context)
            elif message_text == "🏆 Prize Management":
                await self._show_prize_management_menu(update, context)
            elif message_text == "📋 Full Leaderboard":
                context.user_data['admin_page'] = 'leaderboard'
                context.user_data['leaderboard_page'] = 0
                await self._show_admin_leaderboard(update, context)
            elif message_text == "🔙 Back to Admin Menu":
                context.user_data['admin_page'] = 'main'
                await self._show_admin_main_menu(update, context)

            # User Management submenu
            elif message_text == "💰 Add Tokens":
                context.user_data['admin_page'] = 'add_tokens'
                await self._show_add_tokens_interface(update, context)
            elif message_text == "💸 Remove Tokens":
                context.user_data['admin_page'] = 'remove_tokens'
                await self._show_remove_tokens_interface(update, context)

            # Prize Management submenu
            elif message_text == "🏆 View Top 5":
                await self._show_top_5_for_prizes(update, context)
            elif message_text == "💸 Deduct from Top 5":
                await self._show_deduct_from_top_5(update, context)

            # Leaderboard navigation
            elif message_text == "⬅️ Previous Page":
                if context.user_data.get('admin_page') == 'leaderboard':
                    page = context.user_data.get('leaderboard_page', 0)
                    if page > 0:
                        context.user_data['leaderboard_page'] = page - 1
                        await self._show_admin_leaderboard(update, context)
            elif message_text == "➡️ Next Page":
                if context.user_data.get('admin_page') == 'leaderboard':
                    page = context.user_data.get('leaderboard_page', 0)
                    context.user_data['leaderboard_page'] = page + 1
                    await self._show_admin_leaderboard(update, context)

            # Handle token operations
            elif context.user_data.get('admin_page') in ['add_tokens', 'remove_tokens']:
                await self._handle_token_operation(update, context, message_text)
            elif context.user_data.get('admin_page') == 'deduct_top_5':
                await self._handle_deduct_top_5_operation(update, context, message_text)

            else:
                await update.message.reply_text("❌ **Unknown admin command. Please use the menu buttons.**")

        except Exception as e:
            logger.error(f"Error handling admin message: {e}")
            await update.message.reply_text("❌ **Error processing admin command.**")

    async def _show_detailed_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show detailed bot statistics"""
        try:
            stats = await self._get_bot_statistics()

            # Get additional statistics
            active_users = await self.database.users.count_documents({'is_active': True})
            banned_users = await self.database.users.count_documents({'is_banned': True})

            # Get top 3 referrers for detailed view
            top_referrers = await self.user_service.get_leaderboard(page=0, per_page=3, sort_by='referral_count')

            top_referrers_text = ""
            for i, user in enumerate(top_referrers, 1):
                username = f"@{user.username}" if user.username else f"ID: {user.user_id}"
                top_referrers_text += f"**{i}.** {username} - `{user.referral_count}` referrals\n"

            if not top_referrers_text:
                top_referrers_text = "*No referrals yet*"

            detailed_text = f"""
╔══════════════════════════════╗
║    📊 **DETAILED BOT STATISTICS** 📊    ║
╚══════════════════════════════╝

👥 **USER STATISTICS**
• **Total Users:** `{stats['total_users']}`
• **Active Users:** `{active_users}`
• **Banned Users:** `{banned_users}`

📅 **GROWTH METRICS**
• **Today:** `{stats['users_today']}` new users
• **Yesterday:** `{stats['users_yesterday']}` new users
• **Last 7 days:** `{stats['users_week']}` new users

🔗 **REFERRAL METRICS**
• **Total Referrals:** `{stats['total_referrals']}`
• **Top Referrer:** {stats['top_referrer']['display']}

🏆 **TOP 3 REFERRERS**
{top_referrers_text}

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
            """

            keyboard = [[KeyboardButton("🔙 Back to Admin Menu")]]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                detailed_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing detailed statistics: {e}")
            await update.message.reply_text("❌ **Error loading detailed statistics.**")

    async def _show_user_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user management menu"""
        try:
            context.user_data['admin_page'] = 'user_management'

            menu_text = """
╔══════════════════════════════╗
║    👥 **USER MANAGEMENT** 👥    ║
╚══════════════════════════════╝

🛠️ **Select a user management function:**

💰 **Add Tokens** - Add Genesis Tokens to a user's balance
💸 **Remove Tokens** - Remove Genesis Tokens from a user's balance
📋 **Full Leaderboard** - View complete leaderboard with pagination

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
            """

            keyboard = [
                [KeyboardButton("💰 Add Tokens"), KeyboardButton("💸 Remove Tokens")],
                [KeyboardButton("📋 Full Leaderboard")],
                [KeyboardButton("🔙 Back to Admin Menu")]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing user management menu: {e}")
            await update.message.reply_text("❌ **Error loading user management menu.**")

    async def _show_prize_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show prize management menu"""
        try:
            context.user_data['admin_page'] = 'prize_management'

            menu_text = """
╔══════════════════════════════╗
║    🏆 **PRIZE MANAGEMENT** 🏆    ║
╚══════════════════════════════╝

🎁 **Manage top referrer rewards and prize distribution:**

🏆 **View Top 5** - See current top 5 referrers
💸 **Deduct from Top 5** - Remove tokens after prize distribution

⚠️ **Use with caution - these operations affect user balances**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
            """

            keyboard = [
                [KeyboardButton("🏆 View Top 5"), KeyboardButton("💸 Deduct from Top 5")],
                [KeyboardButton("🔙 Back to Admin Menu")]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing prize management menu: {e}")
            await update.message.reply_text("❌ **Error loading prize management menu.**")

    async def _show_add_tokens_interface(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show add tokens interface"""
        try:
            interface_text = """
╔══════════════════════════════╗
║    💰 **ADD GENESIS TOKENS** 💰    ║
╚══════════════════════════════╝

📝 **Enter the details in this format:**
`USER_ID AMOUNT`

**Example:** `123456789 100`

This will add 100 Genesis Tokens to user 123456789's balance.

⚠️ **Make sure the user ID is correct!**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
            """

            keyboard = [[KeyboardButton("🔙 Back to Admin Menu")]]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                interface_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing add tokens interface: {e}")
            await update.message.reply_text("❌ **Error loading add tokens interface.**")

    async def _show_remove_tokens_interface(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show remove tokens interface"""
        try:
            interface_text = """
╔══════════════════════════════╗
║    💸 **REMOVE GENESIS TOKENS** 💸    ║
╚══════════════════════════════╝

📝 **Enter the details in this format:**
`USER_ID AMOUNT`

**Example:** `123456789 50`

This will remove 50 Genesis Tokens from user 123456789's balance.

⚠️ **Make sure the user ID is correct!**
⚠️ **Cannot remove more tokens than user has!**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
            """

            keyboard = [[KeyboardButton("🔙 Back to Admin Menu")]]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                interface_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing remove tokens interface: {e}")
            await update.message.reply_text("❌ **Error loading remove tokens interface.**")

    async def _handle_token_operation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str):
        """Handle token add/remove operations"""
        try:
            # Parse the input: USER_ID AMOUNT
            parts = message_text.strip().split()
            if len(parts) != 2:
                await update.message.reply_text("""
❌ **Invalid format!**

Please use: `USER_ID AMOUNT`
Example: `123456789 100`
                """, parse_mode='Markdown')
                return

            try:
                user_id = int(parts[0])
                amount = int(parts[1])
            except ValueError:
                await update.message.reply_text("""
❌ **Invalid numbers!**

Please ensure both USER_ID and AMOUNT are valid numbers.
                """)
                return

            if amount <= 0:
                await update.message.reply_text("❌ **Amount must be greater than 0!**")
                return

            # Get user
            user = await self.user_service.get_user(user_id)
            if not user:
                await update.message.reply_text(f"❌ **User {user_id} not found!**")
                return

            operation = context.user_data.get('admin_page')
            admin_id = update.effective_user.id

            if operation == 'add_tokens':
                # Add tokens
                success = await self._add_tokens_to_user(user_id, amount, admin_id)
                if success:
                    new_balance = user.balance + amount
                    await update.message.reply_text(f"""
✅ **Tokens Added Successfully!**

👤 **User:** {user_id} (@{user.username if user.username else 'N/A'})
💰 **Added:** {amount} Genesis Tokens
🏦 **New Balance:** {new_balance} Genesis Tokens
                    """, parse_mode='Markdown')
                else:
                    await update.message.reply_text("❌ **Failed to add tokens!**")

            elif operation == 'remove_tokens':
                # Check if user has enough balance
                if user.balance < amount:
                    await update.message.reply_text(f"""
❌ **Insufficient Balance!**

👤 **User:** {user_id}
💰 **Current Balance:** {user.balance} Genesis Tokens
💸 **Requested Removal:** {amount} Genesis Tokens

Cannot remove more tokens than user has!
                    """, parse_mode='Markdown')
                    return

                # Remove tokens
                success = await self._remove_tokens_from_user(user_id, amount, admin_id)
                if success:
                    new_balance = user.balance - amount
                    await update.message.reply_text(f"""
✅ **Tokens Removed Successfully!**

👤 **User:** {user_id} (@{user.username if user.username else 'N/A'})
💸 **Removed:** {amount} Genesis Tokens
🏦 **New Balance:** {new_balance} Genesis Tokens
                    """, parse_mode='Markdown')
                else:
                    await update.message.reply_text("❌ **Failed to remove tokens!**")

        except Exception as e:
            logger.error(f"Error handling token operation: {e}")
            await update.message.reply_text("❌ **Error processing token operation.**")

    async def _add_tokens_to_user(self, user_id: int, amount: int, admin_id: int) -> bool:
        """Add tokens to user balance"""
        try:
            # Create admin credit transaction
            transaction = await self.transaction_service.create_transaction(
                user_id=user_id,
                amount=amount,
                transaction_type=TransactionType.ADMIN_CREDIT,
                description=f"Admin credit by {admin_id}",
                reference_id=f"admin_{admin_id}_{datetime.now().timestamp()}"
            )

            if transaction:
                # Complete the transaction
                await self.transaction_service.complete_transaction(transaction.transaction_id, admin_id)

                # Update user balance
                user = await self.user_service.get_user(user_id)
                if user:
                    user.balance += amount
                    await self.user_service.update_user(user)
                    return True

            return False

        except Exception as e:
            logger.error(f"Error adding tokens to user {user_id}: {e}")
            return False

    async def _remove_tokens_from_user(self, user_id: int, amount: int, admin_id: int) -> bool:
        """Remove tokens from user balance"""
        try:
            # Create admin debit transaction
            transaction = await self.transaction_service.create_transaction(
                user_id=user_id,
                amount=-amount,  # Negative amount for debit
                transaction_type=TransactionType.ADMIN_DEBIT,
                description=f"Admin debit by {admin_id}",
                reference_id=f"admin_{admin_id}_{datetime.now().timestamp()}"
            )

            if transaction:
                # Complete the transaction
                await self.transaction_service.complete_transaction(transaction.transaction_id, admin_id)

                # Update user balance
                user = await self.user_service.get_user(user_id)
                if user:
                    user.balance -= amount
                    await self.user_service.update_user(user)
                    return True

            return False

        except Exception as e:
            logger.error(f"Error removing tokens from user {user_id}: {e}")
            return False

    async def _show_admin_leaderboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show admin leaderboard with pagination"""
        try:
            page = context.user_data.get('leaderboard_page', 0)
            per_page = 10

            # Get leaderboard data
            users = await self.user_service.get_leaderboard(page=page, per_page=per_page, sort_by='balance')
            total_pages = await self.user_service.get_leaderboard_total_pages(per_page=per_page, sort_by='balance')

            if not users:
                await update.message.reply_text("📋 **No users found for this page.**")
                return

            leaderboard_text = f"""
╔══════════════════════════════╗
║    📋 **FULL LEADERBOARD** 📋    ║
╚══════════════════════════════╝

📄 **Page {page + 1} of {total_pages}**

"""

            for i, user in enumerate(users, start=(page * per_page) + 1):
                username = f"@{user.username}" if user.username else f"ID: {user.user_id}"
                leaderboard_text += f"**{i}.** {username}\n"
                leaderboard_text += f"    💰 Balance: `{user.balance}` Genesis Tokens\n"
                leaderboard_text += f"    🔗 Referrals: `{user.referral_count}`\n\n"

            leaderboard_text += "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"

            # Create navigation buttons
            keyboard = []
            nav_row = []

            if page > 0:
                nav_row.append(KeyboardButton("⬅️ Previous Page"))
            if page < total_pages - 1:
                nav_row.append(KeyboardButton("➡️ Next Page"))

            if nav_row:
                keyboard.append(nav_row)

            keyboard.append([KeyboardButton("🔙 Back to Admin Menu")])
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                leaderboard_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing admin leaderboard: {e}")
            await update.message.reply_text("❌ **Error loading leaderboard.**")

    async def _show_top_5_for_prizes(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show top 5 users for prize management"""
        try:
            # Get top 5 by referral count
            top_users = await self.user_service.get_leaderboard(page=0, per_page=5, sort_by='referral_count')

            if not top_users:
                await update.message.reply_text("🏆 **No users with referrals found.**")
                return

            top_5_text = """
╔══════════════════════════════╗
║    🏆 **TOP 5 REFERRERS** 🏆    ║
╚══════════════════════════════╝

🎁 **Current top 5 users by referral count:**

"""

            for i, user in enumerate(top_users, 1):
                username = f"@{user.username}" if user.username else f"ID: {user.user_id}"
                top_5_text += f"**{i}.** {username}\n"
                top_5_text += f"    🔗 Referrals: `{user.referral_count}`\n"
                top_5_text += f"    💰 Balance: `{user.balance}` Genesis Tokens\n"
                top_5_text += f"    👤 User ID: `{user.user_id}`\n\n"

            top_5_text += """
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 **Use this information for prize distribution**
            """

            keyboard = [[KeyboardButton("🔙 Back to Admin Menu")]]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                top_5_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing top 5 for prizes: {e}")
            await update.message.reply_text("❌ **Error loading top 5 users.**")

    async def _show_deduct_from_top_5(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show interface to deduct tokens from top 5 users"""
        try:
            # Get top 5 by referral count
            top_users = await self.user_service.get_leaderboard(page=0, per_page=5, sort_by='referral_count')

            if not top_users:
                await update.message.reply_text("🏆 **No users with referrals found.**")
                return

            # Ask for confirmation and amount
            context.user_data['admin_page'] = 'deduct_top_5'
            context.user_data['top_5_users'] = [user.user_id for user in top_users]

            deduct_text = f"""
╔══════════════════════════════╗
║    💸 **DEDUCT FROM TOP 5** 💸    ║
╚══════════════════════════════╝

⚠️ **WARNING: This will deduct tokens from ALL top 5 users!**

🏆 **Current top 5 users:**
"""

            for i, user in enumerate(top_users, 1):
                username = f"@{user.username}" if user.username else f"ID: {user.user_id}"
                deduct_text += f"**{i}.** {username} - `{user.balance}` tokens\n"

            deduct_text += f"""

📝 **Enter the amount to deduct from each user:**
Example: `50` (will deduct 50 tokens from each of the 5 users)

⚠️ **This action cannot be undone!**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
            """

            keyboard = [[KeyboardButton("🔙 Back to Admin Menu")]]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                deduct_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing deduct from top 5: {e}")
            await update.message.reply_text("❌ **Error loading deduct interface.**")

    async def _handle_deduct_top_5_operation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str):
        """Handle deduction from top 5 users"""
        try:
            # Parse the amount
            try:
                amount = int(message_text.strip())
            except ValueError:
                await update.message.reply_text("""
❌ **Invalid amount!**

Please enter a valid number.
Example: `50`
                """)
                return

            if amount <= 0:
                await update.message.reply_text("❌ **Amount must be greater than 0!**")
                return

            top_5_user_ids = context.user_data.get('top_5_users', [])
            if not top_5_user_ids:
                await update.message.reply_text("❌ **No top 5 users data found. Please try again.**")
                return

            admin_id = update.effective_user.id
            successful_deductions = 0
            failed_deductions = 0
            results = []

            # Process each user
            for user_id in top_5_user_ids:
                user = await self.user_service.get_user(user_id)
                if not user:
                    failed_deductions += 1
                    results.append(f"❌ User {user_id}: Not found")
                    continue

                if user.balance < amount:
                    failed_deductions += 1
                    results.append(f"❌ User {user_id}: Insufficient balance ({user.balance} tokens)")
                    continue

                # Deduct tokens
                success = await self._remove_tokens_from_user(user_id, amount, admin_id)
                if success:
                    successful_deductions += 1
                    username = f"@{user.username}" if user.username else f"ID: {user_id}"
                    results.append(f"✅ {username}: {amount} tokens deducted")
                else:
                    failed_deductions += 1
                    results.append(f"❌ User {user_id}: Deduction failed")

            # Show results
            results_text = f"""
╔══════════════════════════════╗
║    📊 **DEDUCTION RESULTS** 📊    ║
╚══════════════════════════════╝

✅ **Successful:** {successful_deductions}
❌ **Failed:** {failed_deductions}

**Details:**
"""

            for result in results:
                results_text += f"{result}\n"

            results_text += "\n▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"

            # Reset admin page
            context.user_data['admin_page'] = 'main'
            context.user_data.pop('top_5_users', None)

            keyboard = [[KeyboardButton("🔙 Back to Admin Menu")]]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            await update.message.reply_text(
                results_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error handling deduct top 5 operation: {e}")
            await update.message.reply_text("❌ **Error processing deduction operation.**")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        try:
            # Skip channel posts and group messages
            if update.channel_post or update.edited_channel_post:
                return

            if update.message and update.message.chat.type in ['group', 'supergroup']:
                return

            if not update.effective_user or not update.message or not update.message.text:
                return

            user_id = update.effective_user.id
            message_text = update.message.text

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Handle admin panel messages
            if context.user_data.get('admin_mode') and user_id in Config.ADMIN_USER_IDS:
                await self._handle_admin_message(update, context, message_text)
                return

            # Handle main menu buttons
            if message_text in ["Genesis Token Balance", "💰 Genesis Token Balance"]:
                await self.balance_command(update, context)
            elif message_text in ["Earn Genesis Token", "🚀 Earn Genesis Token"]:
                await self._handle_referrals_menu(update, context)
            elif message_text in ["How to Earn Genesis Token❓"]:
                await self._handle_how_to_earn(update, context)
            elif message_text in ["Top 5 Users", "🏆 Top 5 Users"]:
                await self._handle_top_users(update, context)
            elif message_text in ["About Genesis Bot", "ℹ️ About Genesis Bot"]:
                await self._handle_about_bot(update, context)
            elif message_text == "🔙 MAIN MENU":
                await update.message.reply_text(
                    """
╔══════════════════════════════╗
║    🏠 **RETURNING TO MAIN MENU** 🏠    ║
╚══════════════════════════════╝

✨ **Welcome back!** ✨
                    """,
                    reply_markup=self.get_main_keyboard(),
                    parse_mode='Markdown'
                )
            else:
                # Default message for unrecognized input
                await update.message.reply_text(
                    """
╔══════════════════════════════╗
║      🎁 **GENESIS BOT MENU** 🎁      ║
╚══════════════════════════════╝

✨ **Use the buttons below to navigate!** ✨
                    """,
                    reply_markup=self.get_main_keyboard(),
                    parse_mode='Markdown'
                )

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **An error occurred. Please try again.** ⚠️
            """)

    async def _handle_referrals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle referrals menu"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referrals: {e}")
                user = None

            if not user:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
                referral_count = 0
                pending_count = 0
                referral_earnings = 0.0
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
                try:
                    referral_count = await self.services['referral'].get_successful_referral_count(user_id)
                    pending_count = await self.services['referral'].get_pending_referrals_count(user_id)
                except:
                    referral_count = user.successful_referrals
                    pending_count = 0

                referral_earnings = referral_count * Config.REFERRAL_REWARD

            # Show pending referrals info if any exist
            pending_info = ""
            if pending_count > 0:
                pending_info = f"""
⏳ **Pending Referrals:** **{pending_count}** *awaiting channel verification*
"""

            referral_text = f"""
╔══════════════════════════════╗
║    🪙 **EARN GENESIS TOKENS** 🪙    ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📊 **YOUR REFERRAL PERFORMANCE**
✅ **Validated Referrals:** **{referral_count}** *people*{pending_info}
💰 **Total Earned:** **{int(referral_earnings):,}** *Genesis Tokens*
🎯 **Per Referral:** **{Config.REFERRAL_REWARD}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🔗 **YOUR REFERRAL LINK**
📋 `{referral_link}`

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 **HOW TO EARN GENESIS TOKENS**
**1.** *Share your link with friends*
**2.** *They join using your link*
**3.** *They must join our update channel*
**4.** *You earn* **{Config.REFERRAL_REWARD} Genesis Tokens** *per validated referral!*
**5.** *Your friend gets* **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens** *for joining!*

⚠️ **Note:** *Referrals are validated only after users join the required channel*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🚀 **SHARE YOUR LINK ON:**
📱 *WhatsApp, Telegram groups*
🌐 *Social media platforms*
👥 *With friends and family*
            """

            # Create copy referral link button with native clipboard copying
            copy_text_button = CopyTextButton(text=referral_link)
            keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", copy_text=copy_text_button)],
                [InlineKeyboardButton("📊 Referral Stats", callback_data="referral_stats")],
                [InlineKeyboardButton("🔙 Main Menu", callback_data="main_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in referrals menu: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load referral information.** ⚠️
            """)

    async def _handle_top_users(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle Top 5 Users leaderboard - simple display without pagination"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Get top 5 users by balance (simple, no pagination)
            try:
                top_users = await self.services['user'].get_leaderboard(page=0, per_page=5, sort_by='balance')

                # Get current user's rank
                current_user = await self.services['user'].get_user(user_id)
                if current_user:
                    # Count users with higher balance
                    higher_balance_count = await self.database.db.users.count_documents(
                        {"balance": {"$gt": current_user.balance}, "is_banned": False, "is_active": True}
                    )
                    current_user_rank = higher_balance_count + 1
                else:
                    current_user_rank = "N/A"

            except Exception as e:
                logger.error(f"Failed to get top 5 users data: {e}")
                await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load leaderboard.** ⚠️
                """)
                return

            # Format simple top 5 message
            leaderboard_text = """
╔══════════════════════════════╗
║  🏆 **TOP 5 GENESIS TOKEN HOLDERS** 🏆  ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

"""

            if top_users:
                for i, user in enumerate(top_users, 1):
                    first_name = user.first_name or 'Anonymous'
                    balance = int(user.balance)

                    # Add medal emojis for top 3
                    if i == 1:
                        medal = "🥇"
                        rank_style = "**"
                    elif i == 2:
                        medal = "🥈"
                        rank_style = "**"
                    elif i == 3:
                        medal = "🥉"
                        rank_style = "**"
                    else:
                        medal = f"🔸 {i}."
                        rank_style = ""

                    leaderboard_text += f"{medal} {rank_style}{first_name}{rank_style} - **{balance:,}** *Genesis Tokens*\n"
            else:
                leaderboard_text += "🎯 **No users with Genesis Tokens yet.**\n💎 **Be the first to earn some!**\n"

            leaderboard_text += f"""
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📍 **YOUR POSITION**
🏆 **Rank:** **#{current_user_rank}**
💰 **Balance:** **{int(current_user.balance) if current_user else 0:,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 *Climb the leaderboard by earning more* **Genesis Tokens!**
🚀 *Share your referral link to earn* **{Config.REFERRAL_REWARD} tokens** *per friend.*
            """

            await update.message.reply_text(
                leaderboard_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in top users handler: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load top users.** ⚠️
            """)

    async def _show_leaderboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                               page: int = 0, sort_by: str = 'balance'):
        """Show paginated leaderboard"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Get leaderboard data with pagination
            per_page = 10
            try:
                top_users = await self.services['user'].get_leaderboard(page=page, per_page=per_page, sort_by=sort_by)
                total_pages = await self.services['user'].get_leaderboard_total_pages(per_page=per_page, sort_by=sort_by)

                # Get current user's rank and data
                current_user = await self.services['user'].get_user(user_id)
                if current_user:
                    # Count users with higher balance
                    higher_balance_count = await self.database.db.users.count_documents(
                        {"balance": {"$gt": current_user.balance}, "is_banned": False, "is_active": True}
                    )
                    current_user_rank = higher_balance_count + 1
                else:
                    current_user_rank = "N/A"

            except Exception as e:
                logger.error(f"Failed to get leaderboard data: {e}")
                await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load leaderboard.** ⚠️
                """)
                return

            # Format leaderboard message
            sort_title = {
                'balance': 'GENESIS TOKEN HOLDERS',
                'referral_count': 'REFERRAL LEADERS',
                'total_earned': 'TOP EARNERS',
                'successful_referrals': 'REFERRAL CHAMPIONS'
            }.get(sort_by, 'LEADERBOARD')

            leaderboard_text = f"""
╔══════════════════════════════╗
║  🏆 **{sort_title}** 🏆  ║
╚══════════════════════════════╝

📄 **Page {page + 1} of {total_pages}**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

"""

            if top_users:
                for i, user in enumerate(top_users, 1):
                    rank = (page * per_page) + i
                    first_name = user.first_name or 'Anonymous'

                    if sort_by == 'balance':
                        value = f"{int(user.balance):,} Genesis Tokens"
                    elif sort_by == 'referral_count':
                        value = f"{user.referral_count} referrals"
                    elif sort_by == 'total_earned':
                        value = f"{int(user.total_earned):,} tokens earned"
                    else:
                        value = f"{user.successful_referrals} successful"

                    # Add medal emojis for top 3 on first page
                    if page == 0 and rank <= 3:
                        medals = ["🥇", "🥈", "🥉"]
                        medal = medals[rank - 1]
                        rank_style = "**"
                    else:
                        medal = f"🔸 {rank}."
                        rank_style = ""

                    leaderboard_text += f"{medal} {rank_style}{first_name}{rank_style} - **{value}**\n"
            else:
                leaderboard_text += "🎯 **No users found for this page.**\n"

            leaderboard_text += f"""
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📍 **YOUR POSITION**
🏆 **Rank:** **#{current_user_rank}**
💰 **Balance:** **{int(current_user.balance) if current_user else 0:,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 *Climb the leaderboard by earning more* **Genesis Tokens!**
🚀 *Share your referral link to earn* **{Config.REFERRAL_REWARD} tokens** *per friend.*
            """

            # Create navigation buttons
            keyboard = []
            nav_row = []

            # Previous page button
            if page > 0:
                nav_row.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"leaderboard_page_{page-1}_{sort_by}"))

            # Page indicator
            nav_row.append(InlineKeyboardButton(f"📄 {page + 1}/{total_pages}", callback_data="noop"))

            # Next page button
            if page < total_pages - 1:
                nav_row.append(InlineKeyboardButton("Next ➡️", callback_data=f"leaderboard_page_{page+1}_{sort_by}"))

            if nav_row:
                keyboard.append(nav_row)

            # Sort options
            sort_row = []
            if sort_by != 'balance':
                sort_row.append(InlineKeyboardButton("💰 Balance", callback_data=f"leaderboard_page_0_balance"))
            if sort_by != 'referral_count':
                sort_row.append(InlineKeyboardButton("👥 Referrals", callback_data=f"leaderboard_page_0_referral_count"))

            if sort_row:
                keyboard.append(sort_row)

            reply_markup = InlineKeyboardMarkup(keyboard) if keyboard else None

            await update.message.reply_text(
                leaderboard_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in top users handler: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load leaderboard.** ⚠️
            """)

    async def _handle_about_bot(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle About Genesis Bot information"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            about_text = """
╔══════════════════════════════╗
║  🚀 **GENESIS WHALES BOT** 🐋  ║
╚══════════════════════════════╝

🌟 **Powered by Genesis Crypto Whales**

*Your gateway to airdrops, giveaways, and* **exclusive rewards** *starts here.*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🎁 **WHAT IS IT?**

*Genesis Whales Bot is an* **official rewards system** *created to distribute airdrops, gifts, and weekly prizes to our community.*

✅ **Invite friends**
✅ **Earn Genesis Tokens**
✅ **Climb the leaderboard**
✅ **Win guaranteed rewards**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🪙 **HOW IT WORKS**

💰 *Every friend you refer =* **50 Genesis Tokens** *for you*
🎁 *Your friend also gets* **25 Genesis Tokens** *for joining with your link!*

🏆 *The* **Top 5 users** *on the leaderboard each week win* **guaranteed prizes**
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📝 **STEPS TO START EARNING**
**1.** *Tap* **"Earn Genesis Tokens"** *in the bot*
**2.** *Copy your unique referral link*
**3.** *Share it with your network (Telegram, WhatsApp, X, anywhere)*
**4.** *Earn tokens as your friends join through your link*
**5.** *Track your rank in the* **Leaderboard** *tab*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🎯 **The more you share, the more you earn.**
💎 *Start stacking* **Genesis Tokens** *and climb your way to* **exclusive drops!**

👉 **Ready to begin?** ✨
            """

            await update.message.reply_text(
                about_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in about bot handler: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load information.** ⚠️
            """)

    async def _handle_how_to_earn(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle How to Earn Genesis Token information"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Get user data for referral link
            try:
                user = await self.services['user'].get_user(user_id)
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code if user else 'LOADING'}"
            except Exception as e:
                logger.error(f"Failed to get user for how to earn: {e}")
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"

            how_to_earn_text = """
🚀 **HOW TO EARN GENESIS TOKENS:**
💰 *Share your referral link with friends*
🎁 *Earn* **50 Genesis Tokens** *per referral*
🎉 *Your friends get* **25 Genesis Tokens** *for joining!*

🏆 *The* **Top 5 users** *on the leaderboard each week win guaranteed prizes*
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Start earning now by inviting your friends!** 🚀
            """

            # Create inline keyboard with copy referral link button
            copy_text_button = CopyTextButton(text=referral_link)
            keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", copy_text=copy_text_button)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                how_to_earn_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in how to earn handler: {e}")
            await update.message.reply_text("""
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load information.** ⚠️
            """)

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries"""
        try:
            query = update.callback_query
            await query.answer()

            user_id = query.from_user.id
            data = query.data

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await self._safe_edit_message(query, permissions['reason'])
                return

            if data == "verify_channels":
                await self._handle_channel_verification_callback(query, context)
            elif data == "referral_stats":
                await self._show_referral_stats(query, context)
            elif data.startswith("leaderboard_page_"):
                await self._handle_leaderboard_pagination(query, context)
            elif data.startswith("view_referrals_"):
                await self._handle_referral_history_pagination(query, context)
            elif data == "main_menu":
                main_menu_text = """
╔══════════════════════════════╗
║        🏠 **MAIN MENU** 🏠        ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🎁 **Use the buttons below to navigate!** ✨
                """
                await self._safe_edit_message(query, main_menu_text, parse_mode='Markdown')
            elif data == "noop":
                # No operation - just acknowledge the callback
                pass
            else:
                await self._safe_edit_message(query, "❌ Unknown action.")

        except Exception as e:
            logger.error(f"Error in callback handler: {e}")
            try:
                await query.answer("❌ An error occurred.")
            except:
                pass

    async def _handle_leaderboard_pagination(self, query, context):
        """Handle leaderboard pagination callbacks"""
        try:
            # Parse callback data: leaderboard_page_{page}_{sort_by}
            parts = query.data.split('_')
            if len(parts) >= 4:
                page = int(parts[2])
                sort_by = parts[3]
            else:
                page = 0
                sort_by = 'balance'

            # Create a fake update object for the leaderboard method
            fake_update = type('obj', (object,), {
                'effective_user': query.from_user,
                'message': query.message
            })

            # Show the requested leaderboard page
            await self._show_leaderboard_callback(query, context, page=page, sort_by=sort_by)

        except Exception as e:
            logger.error(f"Error handling leaderboard pagination: {e}")
            await self._safe_edit_message(query, "❌ Failed to load leaderboard page.")

    async def _show_leaderboard_callback(self, query, context, page: int = 0, sort_by: str = 'balance'):
        """Show paginated leaderboard for callback queries"""
        try:
            user_id = query.from_user.id

            # Get leaderboard data with pagination
            per_page = 10
            try:
                top_users = await self.services['user'].get_leaderboard(page=page, per_page=per_page, sort_by=sort_by)
                total_pages = await self.services['user'].get_leaderboard_total_pages(per_page=per_page, sort_by=sort_by)

                # Get current user's rank and data
                current_user = await self.services['user'].get_user(user_id)
                if current_user:
                    # Count users with higher balance
                    higher_balance_count = await self.database.db.users.count_documents(
                        {"balance": {"$gt": current_user.balance}, "is_banned": False, "is_active": True}
                    )
                    current_user_rank = higher_balance_count + 1
                else:
                    current_user_rank = "N/A"

            except Exception as e:
                logger.error(f"Failed to get leaderboard data: {e}")
                await self._safe_edit_message(query, """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load leaderboard.** ⚠️
                """)
                return

            # Format leaderboard message (same as _show_leaderboard but for callbacks)
            sort_title = {
                'balance': 'GENESIS TOKEN HOLDERS',
                'referral_count': 'REFERRAL LEADERS',
                'total_earned': 'TOP EARNERS',
                'successful_referrals': 'REFERRAL CHAMPIONS'
            }.get(sort_by, 'LEADERBOARD')

            leaderboard_text = f"""
╔══════════════════════════════╗
║  🏆 **{sort_title}** 🏆  ║
╚══════════════════════════════╝

📄 **Page {page + 1} of {total_pages}**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

"""

            if top_users:
                for i, user in enumerate(top_users, 1):
                    rank = (page * per_page) + i
                    first_name = user.first_name or 'Anonymous'

                    if sort_by == 'balance':
                        value = f"{int(user.balance):,} Genesis Tokens"
                    elif sort_by == 'referral_count':
                        value = f"{user.referral_count} referrals"
                    elif sort_by == 'total_earned':
                        value = f"{int(user.total_earned):,} tokens earned"
                    else:
                        value = f"{user.successful_referrals} successful"

                    # Add medal emojis for top 3 on first page
                    if page == 0 and rank <= 3:
                        medals = ["🥇", "🥈", "🥉"]
                        medal = medals[rank - 1]
                        rank_style = "**"
                    else:
                        medal = f"🔸 {rank}."
                        rank_style = ""

                    leaderboard_text += f"{medal} {rank_style}{first_name}{rank_style} - **{value}**\n"
            else:
                leaderboard_text += "🎯 **No users found for this page.**\n"

            leaderboard_text += f"""
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📍 **YOUR POSITION**
🏆 **Rank:** **#{current_user_rank}**
💰 **Balance:** **{int(current_user.balance) if current_user else 0:,}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 *Climb the leaderboard by earning more* **Genesis Tokens!**
🚀 *Share your referral link to earn* **{Config.REFERRAL_REWARD} tokens** *per friend.*
            """

            # Create navigation buttons
            keyboard = []
            nav_row = []

            # Previous page button
            if page > 0:
                nav_row.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"leaderboard_page_{page-1}_{sort_by}"))

            # Page indicator
            nav_row.append(InlineKeyboardButton(f"📄 {page + 1}/{total_pages}", callback_data="noop"))

            # Next page button
            if page < total_pages - 1:
                nav_row.append(InlineKeyboardButton("Next ➡️", callback_data=f"leaderboard_page_{page+1}_{sort_by}"))

            if nav_row:
                keyboard.append(nav_row)

            # Sort options
            sort_row = []
            if sort_by != 'balance':
                sort_row.append(InlineKeyboardButton("💰 Balance", callback_data=f"leaderboard_page_0_balance"))
            if sort_by != 'referral_count':
                sort_row.append(InlineKeyboardButton("👥 Referrals", callback_data=f"leaderboard_page_0_referral_count"))

            if sort_row:
                keyboard.append(sort_row)

            reply_markup = InlineKeyboardMarkup(keyboard) if keyboard else None

            await self._safe_edit_message(query, leaderboard_text, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            logger.error(f"Error showing leaderboard callback: {e}")
            await self._safe_edit_message(query, "❌ Failed to load leaderboard.")

    async def _handle_referral_history_pagination(self, query, context):
        """Handle referral history pagination callbacks"""
        try:
            # Parse callback data: view_referrals_{page}
            parts = query.data.split('_')
            if len(parts) >= 3:
                page = int(parts[2])
            else:
                page = 0

            # Show the requested referral history page
            await self._show_referral_history(query, context, page=page)

        except Exception as e:
            logger.error(f"Error handling referral history pagination: {e}")
            await self._safe_edit_message(query, "❌ Failed to load referral history page.")

    async def _show_referral_history(self, query, context, page: int = 0):
        """Show paginated referral history"""
        try:
            user_id = query.from_user.id
            per_page = 8  # Show 8 referrals per page

            # Get referral history data with pagination
            try:
                referred_users = await self.services['referral'].get_referred_users_paginated(
                    referrer_id=user_id, page=page, per_page=per_page
                )
                total_pages = await self.services['referral'].get_referred_users_total_pages(
                    referrer_id=user_id, per_page=per_page
                )

            except Exception as e:
                logger.error(f"Failed to get referral history data: {e}")
                await self._safe_edit_message(query, """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load referral history.** ⚠️
                """)
                return

            # Format referral history message
            history_text = f"""
╔══════════════════════════════╗
║  👥 **MY REFERRAL HISTORY** 👥  ║
╚══════════════════════════════╝

📄 **Page {page + 1} of {total_pages}**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

"""

            if referred_users:
                for i, referral in enumerate(referred_users, 1):
                    rank = (page * per_page) + i
                    user_name = referral.get('user_name', 'Unknown User')
                    username = referral.get('user_username', '')
                    status = referral.get('status', 'UNKNOWN')
                    user_active = referral.get('user_active', False)
                    user_banned = referral.get('user_banned', True)
                    user_balance = int(referral.get('user_balance', 0))

                    # Format join date
                    created_at = referral.get('created_at')
                    if created_at:
                        join_date = created_at.strftime('%Y-%m-%d')
                    else:
                        join_date = 'Unknown'

                    # Determine status emoji and text
                    if status == 'COMPLETED':
                        status_emoji = "✅"
                        status_text = "Active"
                    elif status == 'PENDING':
                        status_emoji = "⏳"
                        status_text = "Pending"
                    else:
                        status_emoji = "❓"
                        status_text = "Unknown"

                    # Check if user is banned or inactive
                    if user_banned:
                        status_emoji = "🚫"
                        status_text = "Banned"
                    elif not user_active:
                        status_emoji = "💤"
                        status_text = "Inactive"

                    # Format username display
                    username_display = f" (@{username})" if username else ""

                    history_text += f"""
🔸 **{rank}.** **{user_name}**{username_display}
   {status_emoji} *{status_text}* | 📅 *{join_date}* | 💰 *{user_balance:,} tokens*
"""
            else:
                history_text += """
🎯 **No referrals found.**
💡 *Start sharing your referral link to see your referred users here!*
"""

            history_text += """
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 *Only completed referrals earn you Genesis Tokens*
🚀 *Keep sharing to grow your referral network!*
            """

            # Create navigation buttons
            keyboard = []
            nav_row = []

            # Previous page button
            if page > 0:
                nav_row.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"view_referrals_{page-1}"))

            # Page indicator
            if total_pages > 1:
                nav_row.append(InlineKeyboardButton(f"📄 {page + 1}/{total_pages}", callback_data="noop"))

            # Next page button
            if page < total_pages - 1:
                nav_row.append(InlineKeyboardButton("Next ➡️", callback_data=f"view_referrals_{page+1}"))

            if nav_row:
                keyboard.append(nav_row)

            # Back to referral stats button
            keyboard.append([InlineKeyboardButton("🔙 Back to Stats", callback_data="referral_stats")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await self._safe_edit_message(query, history_text, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            logger.error(f"Error showing referral history: {e}")
            await self._safe_edit_message(query, "❌ Failed to load referral history.")

    async def _handle_channel_verification_callback(self, query, context):
        """Handle channel verification callback"""
        try:
            user_id = query.from_user.id

            # Verify channel membership
            is_member = await self._verify_required_channels(user_id, context)

            if is_member:
                # Update user status
                await self._update_user_channel_status(user_id, True)

                # Validate all pending referrals for this user
                validated_count = await self._validate_pending_referrals(user_id)

                # Process legacy pending referral if any (for backward compatibility)
                if 'pending_referral' in context.user_data and 'pending_referrer' in context.user_data:
                    referral_code = context.user_data['pending_referral']
                    referrer_id = context.user_data['pending_referrer']

                    if referrer_id:
                        await self._process_referral(user_id, referrer_id, referral_code, validate_immediately=True)

                    # Clear pending data
                    del context.user_data['pending_referral']
                    del context.user_data['pending_referrer']

                # Get user data for welcome
                user = await self.services['user'].get_user(user_id)
                telegram_user = query.from_user

                # Delete the channel join message
                try:
                    await query.message.delete()
                    logger.debug("Successfully deleted channel join message")
                except Exception as e:
                    logger.error(f"Failed to delete channel join message: {e}")

                # Generate referral link
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code if user else 'LOADING'}"

                # Send the complete welcome message (same as /start command)
                welcome_message = f"""
╔══════════════════════════════╗
║    🎉 **WELCOME TO GENESIS BOT** 🎉    ║
╚══════════════════════════════╝

👋 **Hello {telegram_user.first_name}!**

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💎 **YOUR GENESIS TOKEN BALANCE**
🪙 **{int(user.balance) if user else 0}** *Genesis Tokens*

🔗 **YOUR REFERRAL LINK**
📋 `{referral_link}`

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🚀 **HOW TO EARN GENESIS TOKENS:**
💰 *Share your referral link with friends*
🎁 *Earn* **{Config.REFERRAL_REWARD} Genesis Tokens** *per referral*
🎉 *Your friends get* **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens** *for joining!*

🏆 *The* **Top 5 users** *on the leaderboard each week win* **guaranteed prizes**
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Start earning now by inviting your friends!** 🚀
                """

                # Create inline keyboard with copy referral link button
                copy_text_button = CopyTextButton(text=referral_link)
                inline_keyboard = [
                    [InlineKeyboardButton("📋 Copy Referral Link", copy_text=copy_text_button)]
                ]
                inline_reply_markup = InlineKeyboardMarkup(inline_keyboard)

                # Send the welcome message with copy referral link button
                try:
                    with open('start.jpg', 'rb') as photo_file:
                        await context.bot.send_photo(
                            chat_id=query.message.chat_id,
                            photo=photo_file,
                            caption=welcome_message,
                            parse_mode='Markdown',
                            reply_markup=inline_reply_markup
                        )
                except FileNotFoundError:
                    await context.bot.send_message(
                        chat_id=query.message.chat_id,
                        text=welcome_message,
                        parse_mode='Markdown',
                        reply_markup=inline_reply_markup
                    )

                # Send the main keyboard as a separate message
                await context.bot.send_message(
                    chat_id=query.message.chat_id,
                    text="🏠 **Choose an option from the menu below:**",
                    parse_mode='Markdown',
                    reply_markup=self.get_main_keyboard()
                )

                # Mark registration as completed
                if user:
                    await self.services['user'].mark_registration_completed(user.user_id)

            else:
                failure_message = """
╔══════════════════════════════╗
║    ❌ **VERIFICATION FAILED** ❌    ║
╚══════════════════════════════╝

⚠️ **Please make sure you have joined the channel and try again.** ⚠️
                """

                # Use appropriate edit method based on message type
                await self._safe_edit_message(
                    query,
                    failure_message,
                    parse_mode='Markdown',
                    reply_markup=query.message.reply_markup
                )

        except Exception as e:
            logger.error(f"Error in channel verification callback: {e}")
            error_message = """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Verification failed. Please try again.** ⚠️
            """
            # Use appropriate edit method based on message type
            await self._safe_edit_message(query, error_message)



    async def _show_referral_stats(self, query, context):
        """Show detailed referral statistics"""
        try:
            user_id = query.from_user.id
            user = await self.services['user'].get_user(user_id)

            if user:
                try:
                    referral_count = await self.services['referral'].get_successful_referral_count(user_id)
                    pending_count = await self.services['referral'].get_pending_referrals_count(user_id)
                except:
                    referral_count = user.successful_referrals
                    pending_count = 0

                total_earned = referral_count * Config.REFERRAL_REWARD
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"

                # Show pending referrals info if any exist
                pending_info = ""
                if pending_count > 0:
                    pending_info = f"""
⏳ **Pending Referrals:** **{pending_count}** *awaiting channel verification*
"""

                stats_text = f"""
╔══════════════════════════════╗
║  📊 **DETAILED REFERRAL STATS** 📊  ║
╚══════════════════════════════╝

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

📈 **YOUR PERFORMANCE**
✅ **Validated Referrals:** **{referral_count}** *people*{pending_info}
💰 **Total Earned:** **{int(total_earned):,}** *Genesis Tokens*
🎯 **Per Referral:** **{Config.REFERRAL_REWARD}** *Genesis Tokens*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

🔗 **YOUR REFERRAL LINK**
📋 `{referral_link}`

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

💡 **Note:** *Referrals are validated when users join our update channel*

🚀 **Keep sharing to earn more Genesis Tokens!** ✨
                """

                keyboard = [
                    [InlineKeyboardButton("👥 View My Referrals", callback_data="view_referrals_0")],
                    [InlineKeyboardButton("🔙 Back", callback_data="main_menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await self._safe_edit_message(
                    query,
                    stats_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                error_text = """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load referral statistics.** ⚠️
                """
                await self._safe_edit_message(query, error_text)

        except Exception as e:
            logger.error(f"Error showing referral stats: {e}")
            error_text = """
╔══════════════════════════════╗
║        ❌ **ERROR** ❌        ║
╚══════════════════════════════╝

⚠️ **Failed to load statistics.** ⚠️
            """
            await self._safe_edit_message(query, error_text)

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle errors"""
        logger.error(f"Exception while handling an update: {context.error}")

    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("🔄 Shutting down bot...")
        self.shutdown_in_progress = True
        self.shutdown_event.set()

        if self.database:
            await self.database.close()

        logger.info("✅ Bot shutdown complete")

# ==================== MAIN FUNCTION ====================

async def main():
    """Main function to run the bot"""
    bot = GenesisBotApp()

    try:
        # Create application
        application = Application.builder().token(Config.BOT_TOKEN).build()
        bot.application = application

        # Initialize async components
        await bot.initialize_async_components()

        # Add handlers
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("balance", bot.balance_command))
        application.add_handler(CommandHandler("help", bot.help_command))
        application.add_handler(CommandHandler("admin", bot.admin_command))

        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND & filters.ChatType.PRIVATE, bot.handle_message))
        application.add_handler(CallbackQueryHandler(bot.handle_callback))
        application.add_error_handler(bot.error_handler)

        startup_logger.info("✅ All handlers added successfully")

        # Start the bot manually to avoid event loop issues
        startup_logger.info("🚀 Starting bot in long polling mode...")

        # Initialize and start the application manually
        await application.initialize()
        await application.start()

        # Start the updater
        await application.updater.start_polling(drop_pending_updates=True)

        # Keep the bot running
        try:
            # Create a simple event to keep the bot running
            stop_event = asyncio.Event()

            # Set up signal handlers
            import signal
            def signal_handler(signum, frame):
                logger.info(f"Received signal {signum}, stopping bot...")
                stop_event.set()

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # Wait for stop signal
            await stop_event.wait()

        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, stopping bot...")
        finally:
            # Clean shutdown
            await application.updater.stop()
            await application.stop()
            await application.shutdown()

    except Exception as e:
        logger.error(f"❌ Critical error in main: {e}")
        raise

if __name__ == '__main__':
    try:
        # Use asyncio.run() to handle the event loop properly
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
