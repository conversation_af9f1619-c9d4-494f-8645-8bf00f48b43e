2025-07-31 02:33:56 - __main__ - ERROR - _show_detailed_statistics:973 - Error showing detailed statistics: 'GenesisBotApp' object has no attribute 'user_service'
2025-07-31 02:34:42 - __main__ - ERROR - _handle_token_operation:1197 - Error handling token operation: 'GenesisBotApp' object has no attribute 'user_service'
2025-07-31 02:34:57 - __main__ - ERROR - _handle_token_operation:1197 - Error handling token operation: 'GenesisBotApp' object has no attribute 'user_service'
2025-07-31 02:35:05 - __main__ - ERROR - _show_top_5_for_prizes:1356 - Error showing top 5 for prizes: 'GenesisBotApp' object has no attribute 'user_service'
2025-07-31 02:35:07 - __main__ - ERROR - _show_deduct_from_top_5:1407 - Error showing deduct from top 5: 'GenesisBotApp' object has no attribute 'user_service'
2025-07-31 02:35:10 - __main__ - ERROR - _show_admin_leaderboard:1311 - <PERSON>rror showing admin leaderboard: 'GenesisBotApp' object has no attribute 'user_service'
2025-07-31 02:35:14 - __main__ - ERROR - _show_detailed_statistics:973 - Error showing detailed statistics: 'GenesisBotApp' object has no attribute 'user_service'
2025-07-31 03:23:44 - __main__ - ERROR - _show_detailed_statistics:979 - Error showing detailed statistics: Can't parse entities: can't find end of the entity starting at byte offset 650
2025-07-31 03:41:49 - __main__ - ERROR - initialize_async_components:80 - ❌ Failed to initialize async components: Database connection failed
2025-07-31 03:41:49 - __main__ - ERROR - main:2726 - ❌ Critical error in main: Database connection failed
2025-07-31 03:41:49 - __main__ - ERROR - <module>:2736 - Fatal error: Database connection failed
2025-07-31 03:42:30 - __main__ - ERROR - initialize_async_components:80 - ❌ Failed to initialize async components: Database connection failed
2025-07-31 03:42:30 - __main__ - ERROR - main:2726 - ❌ Critical error in main: Database connection failed
2025-07-31 03:42:30 - __main__ - ERROR - <module>:2736 - Fatal error: Database connection failed
